use std::collections::HashSet;

use macros::gvk;


#[gvk(
    group = "test.halo.run",
    version = "v1",
    kind = "FakeExtension",
    singular = "fakeextension",
    plural = "fakes"
)]
pub struct FakeExtension {
    pub email: String,
    pub tags: HashSet<String>,
}

impl std::hash::Hash for FakeExtension {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.metadata.hash(state);
        self.email.hash(state);
        // Skip tags field since HashSet doesn't implement Hash
    }
}