use halo_model::{GroupVersionKind, GVK};
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize, Default, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub struct Scheme {
    pub group_version_kind: GroupVersionKind,
    pub plural: String,
    pub singular: String,
    pub open_api_schema: Value,
}

impl Scheme {
    pub async fn new<T: GVK>() -> Scheme {
        Scheme {
            group_version_kind: GroupVersionKind {
                group: T::group().to_string(),
                version: T::version().to_string(),
                kind: T::kind().to_string(),
            },
            plural: T::plural().to_string(),
            singular: T::singular().to_string(),
            open_api_schema: Default::default(),
        }
    }
}