[package]
name = "halo-demo"
version = "0.1.0"
edition = "2024"


[dev-dependencies]
pretty_assertions = "1"

[dependencies]
anyhow = "1.0.75"
argon2 = { version = "0.5.2", features = [] }
clia-tracing-config = "0.2.5"
jsonwebtoken = "8.3.0"
once_cell = "1.18.0"
rand = { version = "0.8.5", features = [] }
salvo = { version = "0.58", features = ["anyhow", "logging", "cors", "oapi", "jwt-auth", "rustls", "catch-panic", "cookie", "session"] }
sea-orm = { version = "0", features = ["runtime-tokio-native-tls", "sqlx-mysql"] }
serde = { version = "1.0", features = ["derive"] }
thiserror = "1.0.48"
time = "0.3.28"
tokio = { version = "1", features = ["full"] }
toml = "0.8.0"
tracing = "0.1"
uuid = { version = "1.4.1", features = ["v4", "fast-rng", "macro-diagnostics"] }
chrono = { version = "0.4", features = ["serde"] }
serde_json = "1.0"
log = "0.4.20"
ordermap = "0.5.2"
macros = { path = "./macros" }
halo-model = { path = "./halo-model" }  # 使用共享模型库
pretty_assertions = "1"
