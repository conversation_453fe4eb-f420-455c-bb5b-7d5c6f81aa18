// 模块声明
pub mod metadata;
pub mod group_version_kind;
pub mod extension;
pub mod gvk;

// 预导入模块
pub mod prelude {
    pub use crate::extension::{is_deleted, is_not_deleted, Extension, ExtensionOperator};
    pub use crate::group_version_kind::{GroupKind, GroupVersion, GroupVersionKind};
    pub use crate::metadata::Metadata;
}

// 定义组合 trait
pub trait ExtensionGVK: ExtensionOperator + GVK + 'static {}

// 为所有满足约束的类型自动实现
impl<T> ExtensionGVK for T where T: ExtensionOperator + GVK + 'static {}

pub use extension::{is_deleted, is_not_deleted, Extension, ExtensionOperator};
pub use group_version_kind::{GroupKind, GroupVersion, GroupVersionKind};
pub use gvk::GVK;
// 重新导出主要类型
pub use metadata::Metadata;


